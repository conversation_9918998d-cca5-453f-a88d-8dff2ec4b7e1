<?php
/**
 * Script to repair broken timestamps in existing data
 */

require_once __DIR__ . '/bootstrap.php';

echo "=== Timestamp Repair Script ===\n";

// Get repositories
$categoryRepo = getCategoryRepository();

echo "\n1. Checking categories for broken timestamps...\n";
$categories = $categoryRepo->find();
$brokenCount = 0;

foreach ($categories as $category) {
    if (($category['created_at'] ?? null) === null || ($category['updated_at'] ?? null) === null) {
        $brokenCount++;
        echo "  - Category '{$category['name']}' has broken timestamps\n";
    }
}

echo "Found $brokenCount categories with broken timestamps\n";

if ($brokenCount > 0) {
    echo "\n2. Repairing broken timestamps...\n";
    $repairedCount = $categoryRepo->repairBrokenTimestamps();
    echo "Repaired $repairedCount categories\n";
    
    echo "\n3. Verifying repairs...\n";
    $categoriesAfter = $categoryRepo->find();
    $stillBrokenCount = 0;
    
    foreach ($categoriesAfter as $category) {
        if (($category['created_at'] ?? null) === null || ($category['updated_at'] ?? null) === null) {
            $stillBrokenCount++;
            echo "  - Category '{$category['name']}' still has broken timestamps\n";
        } else {
            echo "  ✓ Category '{$category['name']}' timestamps repaired\n";
        }
    }
    
    echo "\nRepair summary:\n";
    echo "  - Categories with broken timestamps before: $brokenCount\n";
    echo "  - Categories repaired: $repairedCount\n";
    echo "  - Categories still broken: $stillBrokenCount\n";
} else {
    echo "No broken timestamps found - all good!\n";
}

echo "\n=== Repair Complete ===\n";
