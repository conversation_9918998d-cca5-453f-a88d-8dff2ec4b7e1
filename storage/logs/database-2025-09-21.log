[2025-09-21 07:38:47] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-21 07:38:48] INFO: MongoDB connection: success []
[2025-09-21 07:39:10] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-21 07:39:11] INFO: MongoDB connection: success []
[2025-09-21 07:40:18] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-21 07:40:24] INFO: MongoDB connection: success []
[2025-09-21 07:40:49] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-21 07:40:50] INFO: MongoDB connection: success []
[2025-09-21 07:41:28] INFO: MongoDB connection: attempting {"uri":"mongodb://invalid-host:27017"}
[2025-09-21 07:41:28] INFO: MongoDB connection: failed {"error":"No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve 'invalid-host']","code":13053}
[2025-09-21 07:41:28] INFO: MongoDB connection: falling_back_to_mock []
[2025-09-21 07:41:28] INFO: MongoDB connection: mock_initialized []
[2025-09-21 07:41:56] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-21 07:41:57] INFO: MongoDB connection: success []
[2025-09-21 07:42:21] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-21 07:42:22] INFO: MongoDB connection: success []
[2025-09-21 07:42:48] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-21 07:42:49] INFO: MongoDB connection: success []
[2025-09-21 07:43:34] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-21 07:43:35] INFO: MongoDB connection: success []
[2025-09-21 07:44:07] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-21 07:44:08] INFO: MongoDB connection: success []
[2025-09-21 07:44:35] INFO: MongoDB connection: attempting {"uri":"mongodb://invalid-host:27017"}
[2025-09-21 07:44:36] INFO: MongoDB connection: failed {"error":"No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve 'invalid-host']","code":13053}
[2025-09-21 07:44:36] INFO: MongoDB connection: falling_back_to_mock []
[2025-09-21 07:44:36] INFO: MongoDB connection: mock_initialized []
